package com.example.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.time.LocalDateTime;

/**
 * 验证时间必须在未来的验证器
 */
public class FutureTimeValidator implements ConstraintValidator<FutureTime, LocalDateTime> {

    @Override
    public void initialize(FutureTime constraintAnnotation) {
        // 初始化方法，可以在这里获取注解参数
    }

    @Override
    public boolean isValid(LocalDateTime value, ConstraintValidatorContext context) {
        // 如果值为null，让其他验证注解处理
        if (value == null) {
            return true;
        }
        
        // 验证时间是否在当前时间之后
        return value.isAfter(LocalDateTime.now());
    }
}
